{"contractName": "TupleTest", "address": "0x1234567890123456789012345678901234567890", "networks": {"3000": {"address": "0x1234567890123456789012345678901234567890"}}, "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "struct TupleTest.UserData", "name": "userData", "type": "tuple", "components": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "age", "type": "uint256"}, {"internalType": "bool", "name": "active", "type": "bool"}]}], "name": "UserCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "struct TupleTest.UserData[]", "name": "users", "type": "tuple[]", "components": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "age", "type": "uint256"}, {"internalType": "bool", "name": "active", "type": "bool"}]}], "name": "UsersUpdated", "type": "event"}]}