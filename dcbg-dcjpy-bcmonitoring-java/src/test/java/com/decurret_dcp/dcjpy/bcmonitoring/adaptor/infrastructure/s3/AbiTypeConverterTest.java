package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;
import org.web3j.abi.TypeReference;

class AbiTypeConverterTest {

    @Test
    void testConvertType_SupportedTypes() {
        // Test basic supported types
        assertDoesNotThrow(() -> AbiTypeConverter.convertType("uint256", false));
        assertDoesNotThrow(() -> AbiTypeConverter.convertType("string", false));
        assertDoesNotThrow(() -> AbiTypeConverter.convertType("address", false));
        assertDoesNotThrow(() -> AbiTypeConverter.convertType("bool", false));
        assertDoesNotThrow(() -> AbiTypeConverter.convertType("bytes32", false));
    }

    @Test
    void testConvertType_TupleType_Success() {
        // Test that tuple type is now supported
        TypeReference<?> typeRef = AbiTypeConverter.convertType("tuple", false);
        assertNotNull(typeRef);
        assertFalse(typeRef.isIndexed());
    }

    @Test
    void testConvertType_TupleArrayType_Success() {
        // Test that tuple array type is now supported
        TypeReference<?> typeRef = AbiTypeConverter.convertType("tuple[]", false);
        assertNotNull(typeRef);
        assertFalse(typeRef.isIndexed());
    }

    @Test
    void testConvertType_IndexedTupleType_Success() {
        // Test that indexed tuple type is supported
        TypeReference<?> typeRef = AbiTypeConverter.convertType("tuple", true);
        assertNotNull(typeRef);
        assertTrue(typeRef.isIndexed());
    }

    @Test
    void testConvertType_IndexedParameter() {
        // Test indexed parameter creation
        TypeReference<?> typeRef = AbiTypeConverter.convertType("uint256", true);
        assertNotNull(typeRef);
        assertTrue(typeRef.isIndexed());
    }

    @Test
    void testConvertType_NonIndexedParameter() {
        // Test non-indexed parameter creation
        TypeReference<?> typeRef = AbiTypeConverter.convertType("string", false);
        assertNotNull(typeRef);
        assertFalse(typeRef.isIndexed());
    }
}
