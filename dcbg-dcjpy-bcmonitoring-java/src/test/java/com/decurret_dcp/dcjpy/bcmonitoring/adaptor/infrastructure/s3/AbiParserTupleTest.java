package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class AbiParserTupleTest {

    private AbiParser abiParser;

    @BeforeEach
    void setUp() {
        BcmonitoringConfigurationProperties properties = new BcmonitoringConfigurationProperties();
        properties.setAbiFormat("truffle");
        abiParser = new AbiParser(properties);
    }

    @Test
    void testParseAbi_WithTupleTypes_Success() throws IOException {
        // ABI JSON with tuple types
        String abiJson = """
            [
              {
                "anonymous": false,
                "inputs": [
                  {
                    "indexed": true,
                    "internalType": "uint256",
                    "name": "id",
                    "type": "uint256"
                  },
                  {
                    "indexed": false,
                    "internalType": "struct TupleTest.UserData",
                    "name": "userData",
                    "type": "tuple",
                    "components": [
                      {
                        "internalType": "string",
                        "name": "name",
                        "type": "string"
                      },
                      {
                        "internalType": "uint256",
                        "name": "age",
                        "type": "uint256"
                      },
                      {
                        "internalType": "bool",
                        "name": "active",
                        "type": "bool"
                      }
                    ]
                  }
                ],
                "name": "UserCreated",
                "type": "event"
              },
              {
                "anonymous": false,
                "inputs": [
                  {
                    "indexed": false,
                    "internalType": "struct TupleTest.UserData[]",
                    "name": "users",
                    "type": "tuple[]",
                    "components": [
                      {
                        "internalType": "string",
                        "name": "name",
                        "type": "string"
                      },
                      {
                        "internalType": "uint256",
                        "name": "age",
                        "type": "uint256"
                      },
                      {
                        "internalType": "bool",
                        "name": "active",
                        "type": "bool"
                      }
                    ]
                  }
                ],
                "name": "UsersUpdated",
                "type": "event"
              }
            ]
            """;

        // Parse the ABI
        Map<String, AbiParser.ContractAbiEvent> events = abiParser.parseAbi(abiJson);

        // Verify that events were parsed successfully
        assertNotNull(events);
        assertEquals(2, events.size());

        // Verify that both events are present
        boolean hasUserCreated = events.values().stream()
            .anyMatch(event -> "UserCreated".equals(event.getEvent().getName()));
        boolean hasUsersUpdated = events.values().stream()
            .anyMatch(event -> "UsersUpdated".equals(event.getEvent().getName()));

        assertTrue(hasUserCreated, "UserCreated event should be parsed");
        assertTrue(hasUsersUpdated, "UsersUpdated event should be parsed");

        // Verify event inputs
        AbiParser.ContractAbiEvent userCreatedEvent = events.values().stream()
            .filter(event -> "UserCreated".equals(event.getEvent().getName()))
            .findFirst()
            .orElse(null);

        assertNotNull(userCreatedEvent);
        assertEquals(2, userCreatedEvent.getInputs().size());

        // Check first parameter (indexed uint256)
        AbiParser.AbiEventInput idParam = userCreatedEvent.getInputs().get(0);
        assertEquals("id", idParam.getName());
        assertTrue(idParam.isIndexed());

        // Check second parameter (non-indexed tuple)
        AbiParser.AbiEventInput userDataParam = userCreatedEvent.getInputs().get(1);
        assertEquals("userData", userDataParam.getName());
        assertFalse(userDataParam.isIndexed());
    }

    @Test
    void testParseAbi_WithTupleArrayTypes_Success() throws IOException {
        // Test specifically for tuple array types
        String abiJson = """
            [
              {
                "anonymous": false,
                "inputs": [
                  {
                    "indexed": false,
                    "internalType": "struct TupleTest.UserData[]",
                    "name": "users",
                    "type": "tuple[]",
                    "components": [
                      {
                        "internalType": "string",
                        "name": "name",
                        "type": "string"
                      }
                    ]
                  }
                ],
                "name": "UsersUpdated",
                "type": "event"
              }
            ]
            """;

        // This should not throw an exception
        Map<String, AbiParser.ContractAbiEvent> events = assertDoesNotThrow(() -> abiParser.parseAbi(abiJson));

        assertNotNull(events);
        assertEquals(1, events.size());
    }
}
